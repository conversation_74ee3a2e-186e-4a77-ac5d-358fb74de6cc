from pydantic import BaseModel, constr, PositiveInt, ValidationError

class Item(BaseModel):
    name: constr(max_length=10)  # String with max length of 10
    price: PositiveInt  # Integer, must be positive (> 0)

# Example usage with error handling
def process_item(data):
    try:
        item = Item(**data)
        return item
    except ValidationError as e:
        return {"error": str(e)}

# Test cases
if __name__ == "__main__":
    # Valid input
    valid_data = {"name": "First", "price": 100}
    print(process_item(valid_data))  # Output: Item(name='First', price=100)

    # Invalid inputs
    invalid_data_1 = {"name": "Toolongnamehere", "price": 100}
    print(process_item(invalid_data_1))  # Output: {'error': '1 validation error for Item\nname\n  String should have at most 10 characters (type=string_too_long; ...)'}

    invalid_data_2 = {"name": "First", "price": -50}
    print(process_item(invalid_data_2))  # Output: {'error': '1 validation error for Item\nprice\n  Input should be greater than 0 (type=greater_than; ...)'}